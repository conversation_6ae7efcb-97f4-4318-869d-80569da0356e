# 分布式文件扫描系统

## 项目描述
分布式文件扫描系统是一个基于主从架构的远程文件扫描系统，旨在通过从端（Agent）收集文件并上传至主端（Server），由主端进行管理和安全扫描。该系统支持多平台运行，提供高效的文件扫描和安全管理能力。

## 项目架构
```
ExeSentinel_Grid/
├── agent/               # 从端代码
│   ├── simplified_client.go  # 简化版从端程序
│   └── ...              # 其他从端相关代码
├── server/              # 主端代码
│   ├── handlers/        # 路由处理器
│   ├── models/          # 数据模型
│   ├── static/          # 静态文件（前端）
│   ├── main.go          # 主端入口文件
│   └── ...              # 其他主端相关代码
├── README.md            # 项目说明
└── go.mod               # Go 依赖管理文件
```

## 安装和运行指南

### 从端（Agent）
1. 进入 `agent` 目录。
2. 使用 `go build` 编译程序。
3. 运行程序，使用 `-h` 查看命令行参数。

### 主端（Server）
1. 进入 `server` 目录。
2. 初始化 Go 模块：`go mod init <模块名称>`。
3. 使用 `go run main.go` 启动服务器。
4. 打开浏览器访问 `http://localhost:8080` 查看前端界面。

### 依赖安装
请确保在运行程序前安装所有必要的依赖。


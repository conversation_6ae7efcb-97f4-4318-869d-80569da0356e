@echo off
setlocal enabledelayedexpansion

REM =============================
REM ExeSentinel_Grid Build Script
REM =============================

REM Set directories
set RELEASE_DIR=%~dp0release
set FRONTEND_DIR=%~dp0frontend
set BACKEND_DIR=%~dp0backend

REM Create release directory if not exists
if not exist "%RELEASE_DIR%" (
    echo [INFO] Creating release directory: %RELEASE_DIR%
    mkdir "%RELEASE_DIR%"
)

REM Clean previous build artifacts
if exist "%RELEASE_DIR%" (
    echo [INFO] Cleaning previous build artifacts...
    del /Q /F "%RELEASE_DIR%\*"
)

REM =============================
REM Build Frontend
REM =============================
cd /d "%FRONTEND_DIR%"
echo [INFO] Building frontend (Vue 3 + Vite)...
npm install
if errorlevel 1 (
    echo [ERROR] npm install failed in frontend!
    exit /b 1
)
npm run build
if errorlevel 1 (
    echo [ERROR] Frontend build failed!
    exit /b 1
)
REM Copy frontend dist to release
if exist dist (
    xcopy /E /I /Y "dist" "%RELEASE_DIR%\frontend"
    echo [INFO] Frontend build copied to release directory.
) else (
    echo [ERROR] Frontend build output 'dist' not found!
    exit /b 1
)

REM =============================
REM Build Backend
REM =============================
cd /d "%BACKEND_DIR%"
echo [INFO] Building backend (Go static build)...
go mod tidy
if errorlevel 1 (
    echo [ERROR] go mod tidy failed!
    exit /b 1
)
go build -ldflags "-extldflags '-static'" -o "%RELEASE_DIR%\backend.exe" main.go
if errorlevel 1 (
    echo [ERROR] Backend build failed!
    exit /b 1
)
echo [INFO] Backend build output: %RELEASE_DIR%\backend.exe

REM =============================
REM Build Complete
REM =============================
echo [SUCCESS] All components built successfully. Output in: %RELEASE_DIR%
exit /b 0

# Vue 3 + Tailwind CSS Frontend

## 技术栈
- Vue.js 3
- Tailwind CSS
- Vite

## 启动项目

1. 安装依赖：
   ```powershell
   npm install
   ```
2. 启动开发服务器：
   ```powershell
   npm run dev
   ```

访问 http://localhost:5173 查看 Hello World 页面。

## 目录结构

```
server/frontend/
├── src/
│   ├── App.vue
│   ├── main.js
│   └── index.css
├── index.html
├── package.json
├── vite.config.js
├── tailwind.config.js
├── postcss.config.js
└── README.md
```

## 说明
- Hello World 页面已集成 Tailwind CSS 样式。
- 可直接在 `src/App.vue` 修改页面内容。
